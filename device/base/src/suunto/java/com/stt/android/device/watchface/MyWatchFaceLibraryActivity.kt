package com.stt.android.device.watchface

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.device.databinding.ActivityMyWatchfaceListBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MyWatchFaceLibraryActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMyWatchfaceListBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMyWatchfaceListBinding.inflate(layoutInflater)
        setContentView(binding.root)
        /*setContentWithM3Theme {
            val navController = rememberNavController()
            NavHost(
                navController = navController,
                startDestination = WatchFaceLibraryRoute.WATCH_FACE_LIBRARY
            ) {
                watchFaceLibraryDestination(
                    onNavigateUp = { navigateBack(navController) },
                    onShowWatchFaceDetail = ::showFeatureDetails,
                )
            }
        }*/
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context): Intent {
            return Intent(context, MyWatchFaceLibraryActivity::class.java)
        }
    }
}
