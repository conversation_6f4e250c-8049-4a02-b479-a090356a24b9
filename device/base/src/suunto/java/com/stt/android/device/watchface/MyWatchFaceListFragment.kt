package com.stt.android.device.watchface

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.watchface.library.MyWatchFaceLibraryScreen
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MyWatchFaceListFragment : Fragment() {
    private val viewModel by viewModels<MyWatchFaceViewModel>()
    private val pullToSyncViewModel by viewModels<PullToRemoteSyncWatchFaceViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(inflater.context).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
            )

            setContentWithM3Theme {
                MyWatchFaceLibraryScreen(
                    viewModel = viewModel,
                    pullToSyncViewModel = pullToSyncViewModel,
                    onNavigateUp = {
                        findNavController().navigateUp()
                    },
                    onShowWatchFaceDetail = ::showWatchFaceDetail,
                )
            }
        }
    }

    private fun showWatchFaceDetail(watchFace: WatchFace) {
        val action = MyWatchFaceListFragmentDirections
            .actionMyWatchFaceListFragmentToWatchFaceDetailFragment(watchFace)
        findNavController().navigate(action)
    }
}
