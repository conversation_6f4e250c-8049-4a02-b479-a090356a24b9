package com.stt.android.device.watchface

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.device.R
import com.stt.android.device.domain.WatchFaceAndNetworkNotificationViewModel
import com.stt.android.device.watchface.details.OnlineWatchFaceDetailsViewModel
import com.stt.android.device.watchface.note.WatchFaceNote
import dagger.hilt.android.AndroidEntryPoint
import com.stt.android.R as BaseR

@AndroidEntryPoint
class SuuntoOnlineWatchFaceDetailFragment : Fragment() {

    private val viewModel: OnlineWatchFaceDetailsViewModel by viewModels()
    private val watchFaceAndNetworkNotificationViewModel: WatchFaceAndNetworkNotificationViewModel by viewModels()
    private val args: SuuntoOnlineWatchFaceDetailFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Pass the watch face data to the ViewModel
        viewModel.loadWatchFaceDetails(args.watchFaceListItem.id)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(inflater.context).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        val watchFaceListItem = args.watchFaceListItem

        setContentWithM3Theme {
            val viewState by viewModel.viewState.collectAsState()
            val libraryOperationViewState by viewModel.libraryOperationViewState.collectAsState()
            val watchAndNetworkNotificationState by watchFaceAndNetworkNotificationViewModel.watchFaceAndNetworkNotificationState.collectAsState()

            val operationInProgress =
                libraryOperationViewState?.state == LibraryOperationViewState.State.IN_PROGRESS

            val toastData = libraryOperationViewState
                ?.takeUnless { watchAndNetworkNotificationState.watchSyncing }
                ?.toastData()

            LaunchedEffect(toastData) {
                val message = toastData?.message ?: return@LaunchedEffect
                watchFaceAndNetworkNotificationViewModel.showToastMessage(
                    message = message,
                    actionText = toastData.actionText
                )
            }

            Scaffold(
                topBar = {
                    OnlineWatchFaceDetailsTopBar(
                        title = watchFaceListItem.name,
                        onUpPressed = { findNavController().navigateUp() },
                        enableLibraryActions = !operationInProgress,
                        showAddToLibraryAction =
                            (viewState as? OnlineWatchFaceDetailsViewModel.ViewData.Loaded)?.showAddToLibraryAction
                                ?: false,
                        showRemoveFromLibraryAction =
                            (viewState as? OnlineWatchFaceDetailsViewModel.ViewData.Loaded)?.showRemoveFromLibraryAction
                                ?: false,
                        onAddToLibraryAction = {
                            viewModel.addToLibrary()
                        },
                        onRemoveFromLibraryAction = {
                            viewModel.removeFromLibrary()
                        },
                    )
                }
            ) { paddingValues ->
                when (val currentState = viewState) {
                    is OnlineWatchFaceDetailsViewModel.ViewData.Loading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(paddingValues),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }

                    is OnlineWatchFaceDetailsViewModel.ViewData.Loaded -> {
                        Box {
                            OnlineWatchFaceDetailScreen(
                                watchFace = currentState.watchFaceEntity,
                                watchFaceState = currentState.watchFaceStatus,
                                watchFaceNotes = currentState.watchFaceNotes,
                                showInstallButton = currentState.showInstallButton,
                                showSetAsCurrentWatchfaceButton = currentState.showSetAsCurrentWatchfaceButton,
                                enableInstallButton = currentState.enableInstallButton,
                                enableUninstallButton = currentState.enableUninstallButton,
                                watchFaceAndNetworkNotificationState = watchAndNetworkNotificationState,
                                onInstallClick = currentState.onInstallClick,
                                onSetAsCurrentWatchfaceClick = currentState.onSetAsCurrentWatchfaceClick,
                                onUninstallClick = currentState.onUninstallClick,
                                onNoteAction = { action ->
                                    when (action) {
                                        WatchFaceNote.NoteButtonAction.NAVIGATE_TO_LIBRARY -> onNavigateToLibrary()
                                    }
                                },
                                onCustomToastActionClick = {
                                    onNavigateToLibrary()
                                },
                                modifier = Modifier
                                    .padding(paddingValues)
                                    .narrowContent()
                                    .background(MaterialTheme.colorScheme.surface)
                            )

                            if (currentState.confirmData != null) {
                                ConfirmationDialog(
                                    text = stringResource(currentState.confirmData.confirmationTextResId),
                                    cancelButtonText = stringResource(id = BaseR.string.cancel),
                                    confirmButtonText = stringResource(id = BaseR.string.delete),
                                    onDismissRequest = currentState.confirmData.onCancel,
                                    onConfirm = currentState.confirmData.onConfirm,
                                    useDestructiveColorForConfirm = true
                                )
                            }
                        }
                    }

                    is OnlineWatchFaceDetailsViewModel.ViewData.Failed -> {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(paddingValues),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = currentState.errorMsg,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
            }
        }
    }

    private fun onNavigateToLibrary() {
        findNavController().navigate(R.id.myWatchFaceListFragment)
    }
}
