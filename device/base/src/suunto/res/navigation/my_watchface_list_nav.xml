<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:startDestination="@id/myWatchFaceListFragment">

    <fragment
        android:id="@+id/myWatchFaceListFragment"
        android:name="com.stt.android.device.watchface.MyWatchFaceListFragment"
        android:label="MyWatchFaceLibraryListFragment">

        <action
            android:id="@+id/action_myWatchFaceListFragment_to_watchFaceDetailFragment"
            app:destination="@id/watchFaceDetailFragment" />

    </fragment>

    <fragment
        android:id="@+id/watchFaceDetailFragment"
        android:name="com.stt.android.device.watchface.SuuntoOnlineWatchFaceDetailFragment"
        android:label="MyWatchFaceDetailFragment">

        <argument
            android:name="watchFaceListItem"
            app:argType="com.stt.android.device.domain.watchface.WatchFace"
            app:nullable="false" />

    </fragment>

</navigation>
