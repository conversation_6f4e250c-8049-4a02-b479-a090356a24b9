<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:startDestination="@id/watchFaceListFragment">

    <fragment
        android:id="@+id/watchFaceListFragment"
        android:name="com.stt.android.device.watchface.SuuntoOnlineWatchFaceListFragment"
        android:label="SuuntoOnlineWatchFaceFragment">

        <action
            android:id="@+id/action_watchFaceListFragment_to_watchFaceDetailFragment"
            app:destination="@id/watchFaceDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/watchFaceDetailFragment"
        android:name="com.stt.android.device.watchface.SuuntoOnlineWatchFaceDetailFragment"
        android:label="SuuntoOnlineWatchFaceDetailFragment">

        <argument
            android:name="watchFaceListItem"
            app:argType="com.stt.android.device.domain.watchface.WatchFace"
            app:nullable="false" />

        <action
            android:id="@+id/action_watchFaceDetailFragment_to_myWatchFaceListFragment"
            app:destination="@id/myWatchFaceListFragment" />

    </fragment>

    <fragment
        android:id="@+id/myWatchFaceListFragment"
        android:name="com.stt.android.device.watchface.MyWatchFaceListFragment"
        android:label="MyWatchFaceListFragment">

    </fragment>

</navigation>
